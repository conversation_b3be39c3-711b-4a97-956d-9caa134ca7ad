import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Toast, Checkbox } from "antd-mobile";
import { HeartFill } from "antd-mobile-icons";
import styles from "./index.module.scss";
import NavigatorBar from "@/components/NavBar";
import { useHistory, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import { getPoolInfo, listDirectory, StoragePool } from "@/api/fatWall";
import { uploadToBaiduNetdisk, BaiduUploadPathItem } from "@/api/nasDisk";
import { PreloadImage } from "@/components/Image";
import { getFileIcon } from "@/utils/fileTypeUtils";
import { modalShow } from "@/components/List";
import { useUser } from "@/utils/UserContext";
import { useTheme } from "@/utils/themeDetector";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import next from "@/Resources/icon/next.png";
import next_dark from "@/Resources/icon/next_dark.png";
import outlineIcon from "@/Resources/icon/outline.png";
import outlineDarkIcon from "@/Resources/icon/outline-dark.png";
import ChangeLocation from "./ChangeLocation";

interface FileItem {
  id: string;
  name: string;
  type: "folder" | "file";
  time: string;
  isLiked?: boolean;
  path: string;
  isDirectory?: boolean;
  dataDir?: string;
  parent?: string;
  size?: string; // 文件大小，单位为字节（字符串格式）
}

const FileUpload: React.FC = () => {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const location = useLocation<any>();
  const { uploadPath, uploadDisplayPath } = location.state || {};
  const { userInfo } = useUser();
  const { nas_vip } = userInfo || {};

  // 保存从 ChangeLocation 传递回来的文件路径列表
  const [savedFilePaths, setSavedFilePaths] = useState<string[]>(
    location.state?.selectedFolders || []
  );

  // 顶层文件夹列表
  const [topLevelFolders, setTopLevelFolders] = useState<FileItem[]>([]);

  // 选中的文件夹ID列表
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);

  // 当前存储池名称
  const [currentPoolName, setCurrentPoolName] = useState<string>("百度网盘");

  // 存储池列表
  const [, setStoragePools] = useState<StoragePool[]>([]);

  // 面包屑导航路径
  const [breadcrumbPath, setBreadcrumbPath] = useState<
    { label: string; path: string }[]
  >([]);

  // 当前路径
  const [, setCurrentPath] = useState<string>("/");

  // 当前路径的parent值，用于API调用
  const [, setCurrentPathParent] = useState<string>("");

  // webDAV配置信息
  const [, setWebDAVConfig] = useState<{
    alias_root: string;
  } | null>(null);

  // 上传路径状态
  const [currentUploadPath] = useState<string>(
    uploadPath || "/来自：Xiaomi 智能存储"
  );

  // 上传路径显示文本
  const [uploadPathDisplay, setUploadPathDisplay] = useState<string>(
    uploadDisplayPath || "/来自：Xiaomi 智能存储"
  );

  // ChangeLocation组件显示状态
  const [showChangeLocation, setShowChangeLocation] = useState<boolean>(false);

  // 处理ChangeLocation选择
  const handleLocationSelect = useCallback((uploadPath: string, uploadDisplayPath: string) => {
    setUploadPathDisplay(uploadDisplayPath);
    setShowChangeLocation(false);
    Toast.show({
      content: `已设置上传位置：${uploadDisplayPath}`,
      position: 'bottom',
      duration: 2000,
    });
  }, []);

  // 处理ChangeLocation关闭
  const handleLocationClose = useCallback(() => {
    setShowChangeLocation(false);
  }, []);

  // 上传请求
  const { run: runUpload, loading: uploadLoading } = useRequest(
    (params: {
      localPaths: string[];
      remotePath: string;
      autotask: number;
    }) => {
      // 格式化为对象数组格式
      const formattedLocalpath: BaiduUploadPathItem[] = params.localPaths.map(
        (path) => {
          const fileItem = topLevelFolders.find(
            (folder) => folder.path === path
          );
          return {
            type: fileItem?.isDirectory ? "directory" : "file",
            path: path,
          };
        }
      );

      return uploadToBaiduNetdisk({
        action: "upload",
        autotask: params.autotask,
        remotepath: params.remotePath,
        localpath: formattedLocalpath,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result.code === 0) {
          Toast.show({
            icon: "success",
            content: "上传任务添加成功",
          });

          // 返回到网盘文件页面，并传递任务数量
          history.push({
            pathname: "/baiduNetdisk_app",
            state: {
              activeTab: "diskfiles",
              newTaskCount: selectedFolders.length,
            },
          });
        } else {
          Toast.show({
            icon: "fail",
            content: `上传任务添加失败: ${result.errmsg || "未知错误"}`,
          });
        }
      },
      onError: (error) => {
        Toast.show({
          icon: "fail",
          content: "上传请求出错，请重试",
        });
        console.error("上传请求出错:", error);
      },
    }
  );

  // 获取存储池信息
  const { run: fetchPoolInfo, loading: poolLoading } = useRequest(getPoolInfo, {
    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        setStoragePools(response.data.internal_pool);

        // 保存webDAV配置
        if (response.data.webDAV) {
          setWebDAVConfig({
            alias_root: response.data.webDAV.alias_root,
          });
        }

        // 获取第一个存储池的顶层目录作为顶层文件夹
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          // 设置当前存储池名称和面包屑
          setCurrentPoolName(firstPool.name);
          setBreadcrumbPath([{ label: firstPool.name, path: "/" }]);

          let pathParent = firstPool.data_dir;

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;
            pathParent = aliasRoot + dataDir;
          }

          // 保存当前路径信息
          setCurrentPath("/");
          setCurrentPathParent(pathParent);

          // 获取顶层目录
          fetchTopLevelDirectory({
            path: {
              parent: pathParent,
              recursion: false,
            },
          });
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
      Toast.show({
        content: "获取存储池信息失败，请重试",
        position: "bottom",
        duration: 2000,
      });
      setTopLevelFolders([]);
    },
  });

  // 获取顶层目录列表
  const { run: fetchTopLevelDirectory, loading: directoryLoading } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          const files: FileItem[] = response.data.files.map((file, index) => ({
            id: `file_${index}`,
            name: file.name,
            type: file.xattr.directory
              ? ("folder" as const)
              : ("file" as const),
            time: new Date(parseInt(file.modified_time))
              .toLocaleDateString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              })
              .replace(/\//g, "/")
              .replace(/,/g, ""),
            path: `${file.parent}/${file.name}`,
            parent: file.parent,
            isDirectory: file.xattr.directory,
            isLiked: file.xattr.favorite,
            size: file.size, // 保存文件大小信息
          }));
          setTopLevelFolders(files);
        }
      },
      onError: (error) => {
        console.error("获取顶层目录失败：", error);
        Toast.show({
          content: "获取顶层目录失败，请重试",
          position: "bottom",
          duration: 2000,
        });
        setTopLevelFolders([]);
      },
    }
  );

  // 初始化时获取存储池信息
  useEffect(() => {
    fetchPoolInfo({});
  }, [fetchPoolInfo]);

  // 在文件列表加载完成后，根据保存的路径设置选中状态
  useEffect(() => {
    if (topLevelFolders.length > 0 && savedFilePaths.length > 0) {
      const matchedFolderIds = topLevelFolders
        .filter((folder) => {
          // 检查每个文件夹的路径是否在savedFilePaths中
          return savedFilePaths.some((path) => {
            // 完全匹配路径
            if (folder.path === path) {
              return true;
            }

            // 匹配 parent/name 格式的路径
            if (folder.parent && folder.name) {
              const constructedPath = `${folder.parent}/${folder.name}`;
              return path === constructedPath;
            }

            // 文件路径的末尾部分与保存的路径匹配
            const folderPathParts = folder.path?.split("/") || [];
            const savedPathParts = path.split("/");

            if (folderPathParts.length > 0 && savedPathParts.length > 0) {
              const folderName = folderPathParts[folderPathParts.length - 1];
              const savedName = savedPathParts[savedPathParts.length - 1];
              return folderName === savedName;
            }

            return false;
          });
        })
        .map((folder) => folder.id);

      if (matchedFolderIds.length > 0) {
        setSelectedFolders(matchedFolderIds);
        // 清除已使用的savedFilePaths
        setSavedFilePaths([]);
      }
    }
  }, [topLevelFolders, savedFilePaths]);

  // 处理文件夹选中状态
  const handleFolderSelect = useCallback(
    (folderId: string, checked: boolean) => {
      if (checked) {
        setSelectedFolders((prev) => [...prev, folderId]);
      } else {
        setSelectedFolders((prev) => prev.filter((id) => id !== folderId));
      }
    },
    []
  );

  // 处理文件夹导航
  const handleFolderNavigation = useCallback(
    (folder: FileItem) => {
      if (!folder.isDirectory) {
        return;
      }

      // 导航到子文件夹
      setCurrentPath(folder.path);
      setBreadcrumbPath([
        ...breadcrumbPath,
        { label: folder.name, path: folder.path },
      ]);
      setCurrentPathParent(folder.path);

      // 获取子文件夹内容
      fetchTopLevelDirectory({
        path: {
          parent: folder.path,
          recursion: false,
        },
      });

      // 清空选择
      setSelectedFolders([]);
    },
    [breadcrumbPath, fetchTopLevelDirectory]
  );

  // 处理面包屑导航点击
  const handleBreadcrumbClick = useCallback(
    (item: { label: string; path: string }, index: number) => {
      if (index === 0) {
        // 点击根目录（存储池）
        fetchPoolInfo({});
        setBreadcrumbPath([{ label: currentPoolName, path: "/" }]);
        setCurrentPath("/");
      } else {
        // 点击中间路径
        const newPath = breadcrumbPath.slice(0, index + 1);
        setBreadcrumbPath(newPath);

        // 构建路径
        let pathToFetch = item.path;
        if (index > 0) {
          // 使用点击项的路径
          pathToFetch = item.path;
        }
        setCurrentPath(pathToFetch);
        setCurrentPathParent(pathToFetch);

        // 获取该路径下的内容
        fetchTopLevelDirectory({
          path: {
            parent: pathToFetch,
            recursion: false,
          },
        });
      }

      // 清空选择
      setSelectedFolders([]);
    },
    [breadcrumbPath, currentPoolName, fetchPoolInfo, fetchTopLevelDirectory]
  );

  // 处理文件项点击
  const handleFileItemClick = useCallback(
    (folderId: string) => {
      // 切换选中状态
      const isSelected = selectedFolders.includes(folderId);
      handleFolderSelect(folderId, !isSelected);
    },
    [selectedFolders, handleFolderSelect]
  );

  // 处理全选/取消全选
  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        // 全选
        const allFolderIds = topLevelFolders.map((folder) => folder.id);
        setSelectedFolders(allFolderIds);
      } else {
        // 取消全选
        setSelectedFolders([]);
      }
    },
    [topLevelFolders]
  );

  // 检查是否全部选中
  const isAllSelected =
    topLevelFolders.length > 0 &&
    selectedFolders.length === topLevelFolders.length;

  // 计算选中文件的总大小（字节）
  const calculateSelectedFilesSize = useCallback(() => {
    const selectedFiles = topLevelFolders.filter((item) =>
      selectedFolders.includes(item.id)
    );

    let totalSize = 0;
    selectedFiles.forEach((file) => {
      if (file.size) {
        const sizeInBytes = parseInt(file.size);
        if (!isNaN(sizeInBytes)) {
          totalSize += sizeInBytes;
        }
      }
    });

    return totalSize;
  }, [topLevelFolders, selectedFolders]);

  // 检查百度网盘容量是否足够
  const checkBaiduNetdiskCapacity = useCallback(() => {
    if (!userInfo) {
      return
    }

    const selectedFilesSize = calculateSelectedFilesSize();
    console.log('selectedFilesSize: ', selectedFilesSize);
    const { used_space, total_space } = userInfo;
    const remainingSpace = total_space - used_space;
    console.log('remainingSpace: ', remainingSpace);

    return selectedFilesSize <= 25;
  }, [userInfo, calculateSelectedFilesSize]);

  // 显示容量不足弹窗
  const showCapacityInsufficientModal = useCallback(() => {
    modalShow(
      "容量不足",
      "百度网盘剩余容量不足，无法上传此文件。",
      (m) => {
        m.destroy();
      },
      undefined,
      true, // onlyShow = true，只显示确定按钮
      {
        position: 'bottom',
        okBtnText: '知道了',
        okBtnStyle: { backgroundColor: '#4285F4', color: '#FFFFFF' },
      }
    );
  }, []);

  // 处理返回
  const handleBack = () => {
    history.goBack();
  };

  // 处理确定按钮
  const handleConfirm = () => {
    // 上传按钮点击埋点
    window.onetrack?.("track", "nasDisk_baiduNetdisk_upload_confirm_click", {
      uploadPath: uploadPathDisplay,
    });

    if (selectedFolders.length === 0) {
      Toast.show({
        content: "请至少选择一个文件或文件夹",
        position: "bottom",
        duration: 2000,
      });
      return;
    }

    // 检查百度网盘容量是否足够
    if (!checkBaiduNetdiskCapacity()) {
      showCapacityInsufficientModal();
      return;
    }

    // 获取选中的文件和文件夹
    const selectedFilePaths = topLevelFolders
      .filter((item) => selectedFolders.includes(item.id))
      .map((item) => item.path);

    if (selectedFilePaths.length === 0) {
      Toast.show({
        content: "选中的文件路径无效，请重新选择",
        position: "bottom",
        duration: 2000,
      });
      return;
    }

    console.log("选择的文件路径：", selectedFilePaths);

    // 调用上传接口
    runUpload({
      // action:"upload",
      localPaths: selectedFilePaths,
      remotePath: currentUploadPath, // 使用当前设置的上传路径
      autotask: 0,
    });

    // 注意：不在这里跳转，而是在请求成功回调中处理
  };

  const showVipModal = () => {
    // 上传到百度网盘位置选择埋点
    window.onetrack?.("track", "nasDisk_baiduNetdisk_upload_location_click", {
      uploadPath: uploadPathDisplay,
    });

    if (nas_vip === 1) {
      // 显示ChangeLocation组件
      setShowChangeLocation(true);
      return;
    }

    // 开通百度会员弹窗显示埋点
    window.onetrack?.("track", "nasDisk_baiduNetdisk_vip_modal_show");

    modalShow(
      "会员权益",
      "该功能为网盘NAS会员权益，是否要开启？",
      (m) => {
        // 确认按钮点击 - 开通百度会员埋点
        window.onetrack?.("track", "nasDisk_baiduNetdisk_vip_open_click");
        m.destroy();
        history.push(`/baiduNetdisk_app/members`);
      },
      () => {
        // 取消按钮点击
      },
      false,
      {
        position: "bottom",
        okBtnText: "开通会员",
        cancelBtnText: "取消",
        okBtnStyle: { backgroundColor: "#402C00", color: "#E2AE1E" },
      }
    );
  };

  return (
    <div className={styles.synchronizationContainer}>
      <div className={styles.fixedHeader}>
        <NavigatorBar
          onBack={handleBack}
          backIcon={isDarkMode ? arrowLeftDark : arrowLeft}
          right={
            <PreloadImage
              src={isDarkMode ? outlineDarkIcon : outlineIcon}
              onClick={() => handleSelectAll(!isAllSelected)}
              style={{ width: "40px", height: "40px" }}
            />
          }
        />
        <div className={styles.title}>选择要上传的文件</div>

        {/* 面包屑导航 */}
        <div className={styles.breadcrumb}>
          <div className={styles.breadcrumbPath}>
            {breadcrumbPath.length > 3 ? (
              <div className={styles.breadcrumbContent}>
                <div className={styles.breadcrumbItem}>...</div>
                {breadcrumbPath.slice(-2).map((item, index) => (
                  <div
                    className={styles.breadcrumbContent}
                    key={`${item.path}_${index}`}
                  >
                    <div className={styles.breadcrumbNextContainer}>
                      <PreloadImage
                        className={styles.breadcrumbNextImg}
                        src={isDarkMode ? next_dark : next}
                        alt="arrow"
                      />
                    </div>
                    <div
                      onClick={() =>
                        handleBreadcrumbClick(
                          item,
                          breadcrumbPath.length - 2 + index
                        )
                      }
                      className={`${styles.breadcrumbItem} ${
                        index === 1 ? styles.breadcrumbItemActive : ""
                      }`}
                    >
                      {item.label}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              breadcrumbPath.map((item, index) => (
                <div
                  className={styles.breadcrumbContent}
                  key={`${item.path}_${index}`}
                >
                  {index !== 0 && (
                    <div className={styles.breadcrumbNextContainer}>
                      <PreloadImage
                        className={styles.breadcrumbNextImg}
                        src={isDarkMode ? next_dark : next}
                        alt="arrow"
                      />
                    </div>
                  )}
                  <div
                    onClick={() => handleBreadcrumbClick(item, index)}
                    className={`${styles.breadcrumbItem} ${
                      index === breadcrumbPath.length - 1
                        ? styles.breadcrumbItemActive
                        : ""
                    }`}
                  >
                    {item.label}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      <div className={styles.scrollableContent}>
        {/* 文件列表 */}
        <div className={styles.fileList}>
          {topLevelFolders.map((folder) => (
            <div key={folder.id} className={styles.fileItem}>
              <div
                className={styles.fileContent}
                onClick={() => {
                  if (folder.isDirectory) {
                    handleFolderNavigation(folder);
                  } else {
                    handleFileItemClick(folder.id);
                  }
                }}
                style={{
                  flex: 1,
                  display: "flex",
                  alignItems: "center",
                  cursor: folder.isDirectory ? "pointer" : "default",
                }}
              >
                <div className={styles.fileIcon}>
                  <PreloadImage
                    src={getFileIcon(folder)}
                    alt={folder.isDirectory ? "folder" : "file"}
                    style={{ width: 40, height: 40 }}
                    className={styles.iconImage}
                  />
                </div>
                <div className={styles.fileInfo}>
                  <div className={styles.fileName}>
                    {folder.name}
                    {folder.isLiked && (
                      <HeartFill className={styles.heartIcon} />
                    )}
                  </div>
                  <div className={styles.fileDetails}>{folder.time}</div>
                </div>
              </div>
              <div
                className={styles.checkboxContainer}
                onClick={(e) => e.stopPropagation()}
              >
                <Checkbox
                  checked={selectedFolders.includes(folder.id)}
                  onChange={(checked) => handleFolderSelect(folder.id, checked)}
                />
              </div>
            </div>
          ))}

          {!poolLoading &&
            !directoryLoading &&
            topLevelFolders.length === 0 && (
              <div className={styles.emptyState}>
                <span>该目录下没有文件夹</span>
              </div>
            )}
        </div>
      </div>
      {/* 底部按钮 */}
      <div className={styles.footerButtons}>
        {nas_vip !== 1 && <div className={styles.vipTip}>VIP专享</div>}
        <Button
          block
          color="primary"
          className={styles.locationButton}
          onClick={showVipModal}
        >
          <span className={styles.pathText}>
            上传到:<span>{uploadPathDisplay}</span>
          </span>
        </Button>
        <Button
          block
          color="primary"
          className={styles.downloadButton}
          onClick={handleConfirm}
          disabled={selectedFolders.length === 0 || uploadLoading}
          loading={uploadLoading}
        >
          {uploadLoading ? "上传中..." : `上传 (${selectedFolders.length})`}
        </Button>
      </div>

      {/* ChangeLocation组件 */}
      <ChangeLocation
        visible={showChangeLocation}
        onClose={handleLocationClose}
        onSelect={handleLocationSelect}
        title="更改上传位置"
      />
    </div>
  );
};

export default FileUpload;
