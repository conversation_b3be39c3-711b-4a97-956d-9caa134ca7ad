import React from 'react';
import { Modal } from 'antd';
import styles from './CapacityInsufficientModal.module.scss';

interface CapacityInsufficientModalProps {
  visible: boolean;
  onClose: () => void;
}

const CapacityInsufficientModal: React.FC<CapacityInsufficientModalProps> = ({
  visible,
  onClose
}) => {
  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      closable={false}
      centered
      maskClosable={true}
      className={styles.capacityModal}
      width={343}
    >
      <div className={styles.modalContent}>
        <div className={styles.title}>
          容量不足
        </div>
        <div className={styles.message}>
          百度网盘剩余容量不足，无法上传此文件。
        </div>
        <button className={styles.confirmButton} onClick={onClose}>
          知道了
        </button>
      </div>
    </Modal>
  );
};

export default CapacityInsufficientModal;
